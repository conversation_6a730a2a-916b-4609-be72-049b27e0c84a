/**
 * MidSceneJS 测试执行器
 * 用于执行 AI 生成的测试用例
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class TestExecutor {
    constructor(options = {}) {
        this.options = {
            headless: options.headless !== false,
            timeout: options.timeout || 30000,
            screenshotDir: options.screenshotDir || '../backend/assets/screenshots',
            viewport: options.viewport || { width: 1920, height: 1080 },
            ...options
        };
    }

    /**
     * 执行测试用例
     */
    async executeTestCase(testCase, targetUrl, taskId) {
        let browser;
        const results = {
            success: false,
            taskId,
            testName: testCase.test_name,
            startTime: new Date().toISOString(),
            endTime: null,
            stepResults: [],
            logs: [],
            finalScreenshot: null,
            error: null
        };

        try {
            results.logs.push(`开始执行测试用例: ${testCase.test_name}`);
            
            // 启动浏览器
            browser = await chromium.launch({
                headless: this.options.headless,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            });

            const context = await browser.newContext({
                viewport: this.options.viewport
            });

            const page = await context.newPage();

            // 设置页面超时
            page.setDefaultTimeout(this.options.timeout);

            // 导航到目标页面
            results.logs.push(`导航到: ${targetUrl}`);
            await page.goto(targetUrl, { waitUntil: 'networkidle' });
            await page.waitForTimeout(2000); // 等待页面稳定

            // 执行测试步骤
            for (let i = 0; i < testCase.steps.length; i++) {
                const step = testCase.steps[i];
                const stepResult = await this.executeStep(page, step, i, taskId, results.logs);
                results.stepResults.push(stepResult);

                // 如果步骤失败且不是断言，可以选择继续或停止
                if (stepResult.status === 'failed' && step.action !== 'assert') {
                    results.logs.push(`步骤 ${i + 1} 失败，继续执行后续步骤`);
                }
            }

            // 最终截图
            const finalScreenshotPath = path.join(
                this.options.screenshotDir,
                `${taskId}_final.png`
            );
            await page.screenshot({ 
                path: finalScreenshotPath, 
                fullPage: true 
            });
            results.finalScreenshot = finalScreenshotPath;

            results.success = true;
            results.logs.push('测试用例执行完成');

        } catch (error) {
            results.error = error.message;
            results.logs.push(`执行异常: ${error.message}`);
            console.error('测试执行异常:', error);
        } finally {
            if (browser) {
                await browser.close();
            }
            results.endTime = new Date().toISOString();
        }

        return results;
    }

    /**
     * 执行单个测试步骤
     */
    async executeStep(page, step, stepIndex, taskId, logs) {
        const stepResult = {
            stepIndex,
            action: step.action,
            selector: step.selector,
            value: step.value,
            text: step.text,
            status: 'success',
            message: '',
            screenshotPath: null,
            executionTime: 0,
            errorDetails: null
        };

        const startTime = Date.now();

        try {
            logs.push(`执行步骤 ${stepIndex + 1}: ${step.action} ${step.selector || ''}`);

            switch (step.action) {
                case 'click':
                    await this.handleClick(page, step, stepResult);
                    break;
                case 'type':
                    await this.handleType(page, step, stepResult);
                    break;
                case 'assert':
                    await this.handleAssert(page, step, stepResult);
                    break;
                case 'wait':
                    await this.handleWait(page, step, stepResult);
                    break;
                case 'navigate':
                    await this.handleNavigate(page, step, stepResult);
                    break;
                case 'scroll':
                    await this.handleScroll(page, step, stepResult);
                    break;
                default:
                    throw new Error(`不支持的操作: ${step.action}`);
            }

            // 步骤执行后截图
            const screenshotPath = path.join(
                this.options.screenshotDir,
                `${taskId}_step_${stepIndex + 1}.png`
            );
            await page.screenshot({ path: screenshotPath });
            stepResult.screenshotPath = screenshotPath;

        } catch (error) {
            stepResult.status = 'failed';
            stepResult.message = `步骤执行失败: ${error.message}`;
            stepResult.errorDetails = error.stack;
            logs.push(`步骤 ${stepIndex + 1} 失败: ${error.message}`);

            // 失败时也截图
            try {
                const errorScreenshotPath = path.join(
                    this.options.screenshotDir,
                    `${taskId}_error_step_${stepIndex + 1}.png`
                );
                await page.screenshot({ path: errorScreenshotPath });
                stepResult.screenshotPath = errorScreenshotPath;
            } catch (screenshotError) {
                logs.push(`截图失败: ${screenshotError.message}`);
            }
        }

        stepResult.executionTime = (Date.now() - startTime) / 1000;
        return stepResult;
    }

    /**
     * 处理点击操作
     */
    async handleClick(page, step, stepResult) {
        const timeout = step.timeout || 5000;
        await page.click(step.selector, { timeout });
        stepResult.message = `成功点击元素: ${step.selector}`;
    }

    /**
     * 处理输入操作
     */
    async handleType(page, step, stepResult) {
        const timeout = step.timeout || 5000;
        
        // 先清空输入框
        await page.fill(step.selector, '', { timeout });
        // 然后输入新值
        await page.type(step.selector, step.value || '', { timeout });
        
        stepResult.message = `成功输入文本到: ${step.selector}`;
    }

    /**
     * 处理断言操作
     */
    async handleAssert(page, step, stepResult) {
        const timeout = step.timeout || 5000;
        
        if (step.text) {
            // 文本断言
            const element = await page.locator(step.selector).first();
            await element.waitFor({ timeout });
            const actualText = await element.textContent();
            
            if (actualText && actualText.includes(step.text)) {
                stepResult.message = `断言成功: 找到文本 "${step.text}"`;
            } else {
                throw new Error(`断言失败: 期望文本 "${step.text}", 实际文本 "${actualText}"`);
            }
        } else {
            // 元素存在断言
            const element = await page.locator(step.selector).first();
            await element.waitFor({ timeout });
            stepResult.message = `断言成功: 元素存在 ${step.selector}`;
        }
    }

    /**
     * 处理等待操作
     */
    async handleWait(page, step, stepResult) {
        const waitTime = step.timeout || 1000;
        await page.waitForTimeout(waitTime);
        stepResult.message = `等待 ${waitTime}ms`;
    }

    /**
     * 处理导航操作
     */
    async handleNavigate(page, step, stepResult) {
        await page.goto(step.value, { waitUntil: 'networkidle' });
        stepResult.message = `导航到: ${step.value}`;
    }

    /**
     * 处理滚动操作
     */
    async handleScroll(page, step, stepResult) {
        const scrollDistance = step.value ? parseInt(step.value) : 500;
        await page.evaluate((distance) => {
            window.scrollBy(0, distance);
        }, scrollDistance);
        stepResult.message = `页面滚动 ${scrollDistance}px`;
    }

    /**
     * 获取页面信息（用于页面分析）
     */
    async capturePage(url, screenshotPath) {
        let browser;
        
        try {
            browser = await chromium.launch({
                headless: this.options.headless,
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            });

            const context = await browser.newContext({
                viewport: this.options.viewport
            });

            const page = await context.newPage();
            
            // 导航到页面
            await page.goto(url, { waitUntil: 'networkidle' });
            await page.waitForTimeout(2000);

            // 获取页面标题
            const title = await page.title();

            // 获取 DOM 结构信息
            const domData = await page.evaluate(() => {
                const getElementInfo = (element) => {
                    if (!element || element.nodeType !== 1) return null;
                    
                    const rect = element.getBoundingClientRect();
                    const styles = window.getComputedStyle(element);
                    
                    return {
                        tagName: element.tagName.toLowerCase(),
                        id: element.id || null,
                        className: element.className || null,
                        text: element.textContent?.trim().substring(0, 100) || null,
                        visible: styles.display !== 'none' && styles.visibility !== 'hidden',
                        rect: {
                            x: Math.round(rect.x),
                            y: Math.round(rect.y),
                            width: Math.round(rect.width),
                            height: Math.round(rect.height)
                        },
                        attributes: Array.from(element.attributes).reduce((acc, attr) => {
                            acc[attr.name] = attr.value;
                            return acc;
                        }, {})
                    };
                };
                
                const interactiveElements = [];
                const selectors = [
                    'button', 'input', 'select', 'textarea', 'a[href]',
                    '[onclick]', '[role="button"]', '[tabindex]'
                ];
                
                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        const info = getElementInfo(el);
                        if (info && info.visible && info.rect.width > 0 && info.rect.height > 0) {
                            interactiveElements.push(info);
                        }
                    });
                });
                
                return {
                    title: document.title,
                    url: window.location.href,
                    interactiveElements: interactiveElements.slice(0, 50),
                    forms: Array.from(document.forms).map(form => getElementInfo(form)).filter(Boolean)
                };
            });

            // 截图
            await page.screenshot({ 
                path: screenshotPath,
                fullPage: true,
                type: 'png'
            });

            return {
                success: true,
                title,
                domData: JSON.stringify(domData),
                screenshotPath
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        } finally {
            if (browser) {
                await browser.close();
            }
        }
    }
}

module.exports = TestExecutor;

// 如果直接运行此脚本
if (require.main === module) {
    const executor = new TestExecutor();
    
    // 示例用法
    const testCase = {
        test_name: "示例测试",
        steps: [
            { action: "click", selector: "#test-button" },
            { action: "type", selector: "#input-field", value: "测试文本" },
            { action: "assert", selector: ".result", text: "成功" }
        ]
    };
    
    executor.executeTestCase(testCase, "https://example.com", "test-123")
        .then(result => {
            console.log('测试结果:', JSON.stringify(result, null, 2));
        })
        .catch(error => {
            console.error('测试失败:', error);
        });
}
