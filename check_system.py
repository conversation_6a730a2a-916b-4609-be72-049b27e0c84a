#!/usr/bin/env python3
"""
AI UI 自动化测试系统 - 系统检查脚本
快速检查系统是否准备就绪
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} (满足要求)")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (需要 3.10+)")
        return False

def check_node_version():
    """检查 Node.js 版本"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            major_version = int(version[1:].split('.')[0])
            if major_version >= 16:
                print(f"✅ Node.js {version} (满足要求)")
                return True
            else:
                print(f"❌ Node.js {version} (需要 16+)")
                return False
        else:
            print("❌ Node.js 未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js 未安装")
        return False

def check_file_structure():
    """检查文件结构"""
    required_files = [
        "backend/main.py",
        "backend/requirements.txt",
        "backend/.env.example",
        "frontend/package.json",
        "frontend/src/main.js",
        "scripts/package.json",
        "scripts/test_executor.js",
        "start.sh",
        "README.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("✅ 所有必需文件存在")
        return True

def check_directories():
    """检查并创建必要目录"""
    required_dirs = [
        "assets",
        "assets/screenshots", 
        "assets/reports",
        "assets/tasks"
    ]
    
    created_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            created_dirs.append(dir_path)
    
    if created_dirs:
        print(f"✅ 创建目录: {', '.join(created_dirs)}")
    else:
        print("✅ 所有必需目录存在")
    
    return True

def check_backend_dependencies():
    """检查后端依赖"""
    requirements_file = "backend/requirements.txt"
    if not os.path.exists(requirements_file):
        print("❌ backend/requirements.txt 不存在")
        return False
    
    # 检查虚拟环境
    venv_path = "backend/venv"
    if os.path.exists(venv_path):
        print("✅ Python 虚拟环境存在")
        
        # 检查是否安装了依赖
        pip_path = os.path.join(venv_path, "bin", "pip") if os.name != 'nt' else os.path.join(venv_path, "Scripts", "pip.exe")
        if os.path.exists(pip_path):
            try:
                result = subprocess.run([pip_path, 'list'], capture_output=True, text=True)
                if 'fastapi' in result.stdout and 'uvicorn' in result.stdout:
                    print("✅ 后端依赖已安装")
                    return True
                else:
                    print("⚠️  后端依赖未完全安装")
                    return False
            except:
                print("⚠️  无法检查后端依赖")
                return False
        else:
            print("⚠️  虚拟环境不完整")
            return False
    else:
        print("⚠️  Python 虚拟环境不存在")
        return False

def check_frontend_dependencies():
    """检查前端依赖"""
    node_modules_path = "frontend/node_modules"
    package_json_path = "frontend/package.json"
    
    if not os.path.exists(package_json_path):
        print("❌ frontend/package.json 不存在")
        return False
    
    if os.path.exists(node_modules_path):
        print("✅ 前端依赖已安装")
        return True
    else:
        print("⚠️  前端依赖未安装")
        return False

def check_scripts_dependencies():
    """检查脚本依赖"""
    node_modules_path = "scripts/node_modules"
    package_json_path = "scripts/package.json"
    
    if not os.path.exists(package_json_path):
        print("❌ scripts/package.json 不存在")
        return False
    
    if os.path.exists(node_modules_path):
        print("✅ 脚本依赖已安装")
        return True
    else:
        print("⚠️  脚本依赖未安装")
        return False

def check_environment_variables():
    """检查环境变量"""
    env_file = "backend/.env"
    env_example = "backend/.env.example"
    
    if not os.path.exists(env_example):
        print("❌ backend/.env.example 不存在")
        return False
    
    if os.path.exists(env_file):
        # 检查是否设置了 API Key
        with open(env_file, 'r') as f:
            content = f.read()
            if 'GEMINI_API_KEY=' in content and 'your_gemini_api_key_here' not in content:
                print("✅ 环境变量已配置")
                return True
            else:
                print("⚠️  GEMINI_API_KEY 未设置")
                return False
    else:
        print("⚠️  backend/.env 文件不存在")
        return False

def print_setup_instructions():
    """打印设置说明"""
    print("\n" + "="*60)
    print("🛠️  系统设置说明")
    print("="*60)
    print()
    print("1. 初始化环境:")
    print("   ./start.sh setup")
    print()
    print("2. 配置 API Key:")
    print("   - 复制 backend/.env.example 到 backend/.env")
    print("   - 在 https://makersuite.google.com/app/apikey 获取 Gemini API Key")
    print("   - 编辑 backend/.env 文件，设置 GEMINI_API_KEY")
    print()
    print("3. 启动系统:")
    print("   ./start.sh start")
    print()
    print("4. 访问系统:")
    print("   前端: http://localhost:3000")
    print("   后端: http://localhost:8000")
    print("   API 文档: http://localhost:8000/docs")

def main():
    """主函数"""
    print("🔍 AI UI 自动化测试系统 - 系统检查")
    print("="*50)
    
    checks = [
        ("Python 版本", check_python_version),
        ("Node.js 版本", check_node_version),
        ("文件结构", check_file_structure),
        ("目录结构", check_directories),
        ("后端依赖", check_backend_dependencies),
        ("前端依赖", check_frontend_dependencies),
        ("脚本依赖", check_scripts_dependencies),
        ("环境变量", check_environment_variables),
    ]
    
    results = []
    
    for check_name, check_func in checks:
        print(f"\n🔍 检查 {check_name}...")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ 检查失败: {e}")
            results.append((check_name, False))
    
    # 统计结果
    print("\n" + "="*50)
    print("📊 检查结果摘要")
    print("="*50)
    
    total_checks = len(results)
    passed_checks = sum(1 for _, passed in results if passed)
    failed_checks = total_checks - passed_checks
    
    print(f"总检查项: {total_checks}")
    print(f"通过: {passed_checks}")
    print(f"失败: {failed_checks}")
    
    print("\n详细结果:")
    for check_name, passed in results:
        status = "✅" if passed else "❌"
        print(f"  {status} {check_name}")
    
    if failed_checks == 0:
        print(f"\n🎉 所有检查通过！系统准备就绪。")
        print("运行 './start.sh start' 启动系统")
    else:
        print(f"\n⚠️  有 {failed_checks} 项检查失败。")
        print_setup_instructions()
    
    return failed_checks == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
