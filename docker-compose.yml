version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=False
    volumes:
      - ./assets:/app/assets
      - ./scripts:/app/scripts
    depends_on:
      - scripts
    networks:
      - ai-ui-test-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - ai-ui-test-network

  # 脚本服务 (用于浏览器自动化)
  scripts:
    build:
      context: ./scripts
      dockerfile: Dockerfile
    volumes:
      - ./assets:/app/assets
    networks:
      - ai-ui-test-network

networks:
  ai-ui-test-network:
    driver: bridge

volumes:
  assets_data:
