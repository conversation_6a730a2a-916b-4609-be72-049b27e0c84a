{"name": "ai-ui-test-frontend", "version": "1.0.0", "description": "AI 驱动 UI 自动化测试系统 - 前端", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0", "pinia": "^2.1.7"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.1.0"}}