import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { testApi, reportApi } from '@/api'

export const useTestStore = defineStore('test', () => {
  // 状态
  const currentTask = ref(null)
  const testCases = ref([])
  const executionResult = ref(null)
  const loading = ref(false)
  const error = ref(null)

  // 计算属性
  const hasTestCases = computed(() => testCases.value.length > 0)
  const isExecuting = computed(() => currentTask.value?.status === 'running')

  // 动作
  const submitUrl = async (url, description = '') => {
    loading.value = true
    error.value = null
    
    try {
      const result = await testApi.submitUrl({ url, description })
      currentTask.value = result
      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const generateTestCases = async (url, screenshotPath, domData, userRequirements = '') => {
    loading.value = true
    error.value = null
    
    try {
      const result = await testApi.generateTestCases({
        url,
        screenshot_path: screenshotPath,
        dom_data: domData,
        user_requirements: userRequirements
      })
      currentTask.value = result
      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const runTestCase = async (testCase, url, headless = false) => {
    loading.value = true
    error.value = null
    
    try {
      const result = await testApi.runTestCase({
        test_case: testCase,
        url,
        headless
      })
      currentTask.value = result
      return result
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const getTaskStatus = async (taskId) => {
    try {
      const status = await testApi.getTaskStatus(taskId)
      if (currentTask.value?.task_id === taskId) {
        currentTask.value = { ...currentTask.value, ...status }
      }
      return status
    } catch (err) {
      error.value = err.message
      throw err
    }
  }

  const getTaskData = async (taskId) => {
    loading.value = true
    error.value = null
    
    try {
      const data = await testApi.getTaskData(taskId)
      
      // 根据数据类型设置相应的状态
      if (data.test_cases) {
        testCases.value = data.test_cases
      }
      
      if (data.step_results) {
        executionResult.value = data
      }
      
      return data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const pollTaskStatus = async (taskId, interval = 2000, maxAttempts = 30) => {
    let attempts = 0
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++
          const status = await getTaskStatus(taskId)
          
          if (status.status === 'completed' || status.status === 'failed') {
            resolve(status)
          } else if (attempts >= maxAttempts) {
            reject(new Error('轮询超时'))
          } else {
            setTimeout(poll, interval)
          }
        } catch (err) {
          reject(err)
        }
      }
      
      poll()
    })
  }

  const clearCurrentTask = () => {
    currentTask.value = null
    testCases.value = []
    executionResult.value = null
    error.value = null
  }

  const setTestCases = (cases) => {
    testCases.value = cases
  }

  const updateTestCase = (index, updatedCase) => {
    if (index >= 0 && index < testCases.value.length) {
      testCases.value[index] = updatedCase
    }
  }

  const deleteTestCase = (index) => {
    if (index >= 0 && index < testCases.value.length) {
      testCases.value.splice(index, 1)
    }
  }

  const addTestCase = (testCase) => {
    testCases.value.push(testCase)
  }

  return {
    // 状态
    currentTask,
    testCases,
    executionResult,
    loading,
    error,
    
    // 计算属性
    hasTestCases,
    isExecuting,
    
    // 动作
    submitUrl,
    generateTestCases,
    runTestCase,
    getTaskStatus,
    getTaskData,
    pollTaskStatus,
    clearCurrentTask,
    setTestCases,
    updateTestCase,
    deleteTestCase,
    addTestCase
  }
})
