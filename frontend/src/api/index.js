import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 可以在这里添加 token 等认证信息
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    const message = error.response?.data?.detail || error.message || '请求失败'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// API 方法
export const testApi = {
  // 提交 URL
  submitUrl: (data) => api.post('/test/submit-url', data),
  
  // 生成测试用例
  generateTestCases: (data) => api.post('/test/generate-testcases', data),
  
  // 执行测试用例
  runTestCase: (data) => api.post('/test/run-testcase', data),
  
  // 获取任务状态
  getTaskStatus: (taskId) => api.get(`/test/task-status/${taskId}`),
  
  // 获取任务数据
  getTaskData: (taskId) => api.get(`/test/task-data/${taskId}`)
}

export const reportApi = {
  // 获取测试结果
  getTestResults: (taskId) => api.get(`/report/results/${taskId}`),
  
  // 获取测试历史
  getTestHistory: (params) => api.get('/report/history', { params }),
  
  // 获取统计信息
  getStatistics: (params) => api.get('/report/statistics', { params }),
  
  // 删除测试结果
  deleteTestResult: (taskId) => api.delete(`/report/results/${taskId}`),
  
  // 导出报告
  exportReport: (taskId, format) => api.post(`/report/export/${taskId}`, { format })
}

export default api
