<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 头部导航 -->
      <el-header class="app-header">
        <div class="header-content">
          <h1 class="app-title">
            <el-icon><Monitor /></el-icon>
            AI UI 自动化测试系统
          </h1>
          <nav class="nav-menu">
            <router-link to="/" class="nav-link">
              <el-button type="primary" :icon="House">首页</el-button>
            </router-link>
            <router-link to="/history" class="nav-link">
              <el-button type="info" :icon="Clock">测试历史</el-button>
            </router-link>
            <router-link to="/statistics" class="nav-link">
              <el-button type="success" :icon="DataAnalysis">统计分析</el-button>
            </router-link>
          </nav>
        </div>
      </el-header>

      <!-- 主要内容区域 -->
      <el-main class="app-main">
        <router-view />
      </el-main>

      <!-- 底部 -->
      <el-footer class="app-footer">
        <div class="footer-content">
          <p>&copy; 2024 AI UI 自动化测试系统 - 基于 Vue 3 + FastAPI + Gemini</p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup>
import { Monitor, House, Clock, DataAnalysis } from '@element-plus/icons-vue'
</script>

<style scoped>
.app-container {
  min-height: 100vh;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.app-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-menu {
  display: flex;
  gap: 10px;
}

.nav-link {
  text-decoration: none;
}

.app-main {
  background: #f5f7fa;
  min-height: calc(100vh - 120px);
  padding: 20px;
}

.app-footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 10px 0;
}

.footer-content p {
  margin: 0;
  font-size: 14px;
}

/* 全局样式 */
:global(body) {
  margin: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

:global(#app) {
  min-height: 100vh;
}
</style>
