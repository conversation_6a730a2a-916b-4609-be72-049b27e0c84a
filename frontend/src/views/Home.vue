<template>
  <div class="home-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <h2>
            <el-icon class="welcome-icon"><Monitor /></el-icon>
            欢迎使用 AI UI 自动化测试系统
          </h2>
          <p class="welcome-description">
            基于 AI 大模型驱动的智能 UI 测试用例生成与执行平台，支持自动化页面分析、测试用例生成和可视化报告。
          </p>
        </div>
      </el-card>
    </div>

    <!-- URL 输入区域 -->
    <div class="url-input-section">
      <el-card class="input-card">
        <template #header>
          <div class="card-header">
            <el-icon><Link /></el-icon>
            <span>输入测试目标网站</span>
          </div>
        </template>

        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="网站 URL" prop="url">
            <el-input
              v-model="form.url"
              placeholder="请输入要测试的网站 URL，例如：https://example.com"
              size="large"
              :prefix-icon="Link"
              clearable
            />
          </el-form-item>

          <el-form-item label="测试描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="可选：描述测试目标或特殊要求"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              @click="handleSubmit"
              :icon="Play"
            >
              {{ loading ? '正在分析页面...' : '开始分析' }}
            </el-button>
            <el-button size="large" @click="resetForm" :icon="Refresh">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 功能特性展示 -->
    <div class="features-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="feature-card">
            <div class="feature-content">
              <el-icon class="feature-icon" color="#409EFF"><Robot /></el-icon>
              <h3>AI 智能分析</h3>
              <p>基于 Gemini 大模型，智能分析页面结构，自动生成高质量测试用例</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="feature-card">
            <div class="feature-content">
              <el-icon class="feature-icon" color="#67C23A"><VideoPlay /></el-icon>
              <h3>自动化执行</h3>
              <p>使用 MidSceneJS 控制浏览器，自动执行测试步骤并记录详细过程</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="feature-card">
            <div class="feature-content">
              <el-icon class="feature-icon" color="#E6A23C"><DataAnalysis /></el-icon>
              <h3>可视化报告</h3>
              <p>生成详细的测试报告，包含步骤截图、执行日志和性能分析</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近测试历史 -->
    <div class="recent-tests-section" v-if="recentTests.length > 0">
      <el-card class="recent-card">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>最近测试</span>
            <router-link to="/history" class="view-all-link">
              查看全部
            </router-link>
          </div>
        </template>

        <el-table :data="recentTests" style="width: 100%">
          <el-table-column prop="test_name" label="测试名称" />
          <el-table-column prop="target_url" label="目标网站" show-overflow-tooltip />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="执行时间" width="180">
            <template #default="scope">
              {{ formatTime(scope.row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="viewReport(scope.row.task_id)"
              >
                查看报告
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Monitor, Link, Play, Refresh, Robot, VideoPlay, DataAnalysis, Clock } from '@element-plus/icons-vue'
import { useTestStore } from '@/stores/test'
import { reportApi } from '@/api'

const router = useRouter()
const testStore = useTestStore()

// 表单数据
const form = reactive({
  url: '',
  description: ''
})

// 表单验证规则
const rules = {
  url: [
    { required: true, message: '请输入网站 URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的 URL', trigger: 'blur' }
  ]
}

const formRef = ref()
const loading = ref(false)
const recentTests = ref([])

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 提交 URL 进行分析
    const result = await testStore.submitUrl(form.url, form.description)
    
    ElMessage.success('页面分析已开始，正在获取页面信息...')
    
    // 轮询任务状态
    const finalStatus = await testStore.pollTaskStatus(result.task_id)
    
    if (finalStatus.status === 'completed') {
      ElMessage.success('页面分析完成！')
      // 跳转到测试用例生成页面
      router.push(`/test-cases/${result.task_id}`)
    } else {
      ElMessage.error('页面分析失败，请检查 URL 是否正确')
    }
    
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(error.message || '提交失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  testStore.clearCurrentTask()
}

// 获取最近测试历史
const loadRecentTests = async () => {
  try {
    const response = await reportApi.getTestHistory({ limit: 5 })
    recentTests.value = response.items || []
  } catch (error) {
    console.error('获取最近测试失败:', error)
  }
}

// 状态类型映射
const getStatusType = (status) => {
  const typeMap = {
    'completed': 'success',
    'failed': 'danger',
    'running': 'warning',
    'processing': 'info'
  }
  return typeMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    'completed': '已完成',
    'failed': '失败',
    'running': '执行中',
    'processing': '处理中'
  }
  return textMap[status] || status
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

// 查看报告
const viewReport = (taskId) => {
  router.push(`/test-report/${taskId}`)
}

onMounted(() => {
  loadRecentTests()
})
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card :deep(.el-card__body) {
  padding: 40px;
}

.welcome-content {
  text-align: center;
}

.welcome-icon {
  font-size: 48px;
  margin-right: 15px;
  vertical-align: middle;
}

.welcome-content h2 {
  margin: 0 0 20px 0;
  font-size: 32px;
  font-weight: 600;
}

.welcome-description {
  font-size: 18px;
  line-height: 1.6;
  margin: 0;
  opacity: 0.9;
}

.url-input-section {
  margin-bottom: 40px;
}

.input-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.features-section {
  margin-bottom: 40px;
}

.feature-card {
  height: 200px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-content {
  text-align: center;
  padding: 20px;
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.feature-content h3 {
  margin: 15px 0 10px 0;
  font-size: 20px;
  color: #303133;
}

.feature-content p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
}

.recent-tests-section {
  margin-bottom: 20px;
}

.recent-card .card-header {
  justify-content: space-between;
}

.view-all-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 14px;
}

.view-all-link:hover {
  text-decoration: underline;
}
</style>
