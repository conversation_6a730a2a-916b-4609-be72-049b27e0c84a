import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import TestCases from '@/views/TestCases.vue'
import TestExecution from '@/views/TestExecution.vue'
import TestReport from '@/views/TestReport.vue'
import History from '@/views/History.vue'
import Statistics from '@/views/Statistics.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { title: '首页' }
  },
  {
    path: '/test-cases/:taskId',
    name: 'TestCases',
    component: TestCases,
    meta: { title: '测试用例' }
  },
  {
    path: '/test-execution/:taskId',
    name: 'TestExecution',
    component: TestExecution,
    meta: { title: '测试执行' }
  },
  {
    path: '/test-report/:taskId',
    name: 'TestReport',
    component: TestReport,
    meta: { title: '测试报告' }
  },
  {
    path: '/history',
    name: 'History',
    component: History,
    meta: { title: '测试历史' }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: Statistics,
    meta: { title: '统计分析' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  document.title = `${to.meta.title} - AI UI 自动化测试系统`
  next()
})

export default router
