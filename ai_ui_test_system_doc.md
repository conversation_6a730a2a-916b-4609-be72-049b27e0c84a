# AI 驱动 UI 自动化测试系统 - 开发文档

## 1. 项目概述
本系统旨在通过大模型驱动，实现自动生成并执行 UI 自动化测试用例，提升测试效率并支持可视化反馈。

## 2. 技术栈

| 模块         | 技术                    |
|--------------|-------------------------|
| 前端         | Vue 3 + JavaScript     |
| 后端         | FastAPI + Python 3.10+ |
| 浏览器控制   | MidSceneJS + Chrome    |
| 大模型       | Gemini 1.5 Pro         |
| 协作框架     | AutoGen                |
| 脚本执行     | 自定义 JSON 用例格式 + MidSceneJS 执行器 |

---

## 3. 系统架构

```
+------------+      +----------------+      +-------------------+
|    用户    | ---> |   Vue 前端 UI  | ---> | FastAPI 后端服务   |
+------------+      +----------------+      +--------+----------+
                                                   |
                                                   v
                    +--------------------------+   <--> Gemini (AutoGen Agent)
                    | MidSceneJS (控制 Chrome) |   <--> 测试专家代理 TestAgent
                    +--------------------------+
                            |
                            v
                 +-----------------------+
                 | 执行测试用例 + 截图记录 |
                 +-----------------------+
                            |
                            v
                 +-----------------------+
                 |  结果反馈 + 可视化报告  |
                 +-----------------------+
```

---

## 4. 模块设计

### 4.1 前端模块（Vue）

- 提供 URL 输入界面和测试按钮
- 展示自动生成的测试用例（可编辑）
- 显示执行结果（步骤、断言、截图）

页面结构：
- 页面链接输入页
- 测试用例展示页
- 测试报告展示页

### 4.2 后端模块（FastAPI）

#### 核心接口：

- `POST /submit-url`
  - 参数：目标网站 URL
  - 功能：启动浏览器、截图页面、发送到 AI 模型

- `POST /generate-testcases`
  - 参数：DOM + 截图数据
  - 功能：AutoGen 调用 Gemini，生成测试 JSON

- `POST /run-testcase`
  - 参数：用例 JSON
  - 功能：MidSceneJS 控制浏览器运行脚本并返回结果

- `GET /results/:task_id`
  - 获取测试执行的结果报告

### 4.3 AI 用例生成模块（AutoGen + Gemini）

- 使用 AutoGen 定义多个 Agent：
  - `TaskAgent`: 接收指令，组织流程
  - `TestAgent`: 输入页面截图和 DOM，调用 Gemini 生成 JSON 用例

用例格式（简化示例）：
```json
{
  "test_name": "Login Test",
  "steps": [
    {"action": "click", "selector": "#login-btn"},
    {"action": "type", "selector": "#username", "value": "testuser"},
    {"action": "type", "selector": "#password", "value": "123456"},
    {"action": "click", "selector": "#submit"},
    {"action": "assert", "selector": ".success", "text": "Welcome"}
  ]
}
```

### 4.4 浏览器控制模块（MidSceneJS）

- MidSceneJS 负责：
  - 打开页面
  - 运行测试 JSON 脚本（JS 执行）
  - 返回执行日志、断言、截图等

### 4.5 报告展示模块

- 后端汇总结果：通过步骤对比、截图、断言状态构建报告 JSON
- 前端展示：
  - 流程步骤（图示）
  - 每步截图
  - 失败高亮

---

## 5. 用例执行流程

1. 用户输入网站链接
2. MidSceneJS 打开网站，截屏并获取 DOM
3. FastAPI 发送截图/DOM 给 AutoGen -> Gemini 生成用例
4. 用户查看/编辑用例，点击“执行”
5. MidSceneJS 按照步骤执行测试脚本
6. 结果反馈，前端展示报告

---

## 6. 项目结构建议

```
project-root/
├── frontend/ (Vue 项目)
│   ├── views/
│   ├── components/
│   └── api/ (调用后端)
│
├── backend/ (FastAPI)
│   ├── main.py
│   ├── routes/
│   ├── services/
│   ├── autogen_agents/
│   └── schemas/
│
├── scripts/
│   └── test_executor.js (MidSceneJS 执行逻辑)
│
├── assets/
│   └── screenshots/
│
└── README.md
```

---

## 7. 安全性与扩展建议

- 控制 AI 输出：使用正则/AST 校验生成用例结构
- 用户项目隔离：用例、报告、截图独立存储
- 支持用例版本管理
- 增加用户行为录制（RPA 扩展）作为补充方式

---

## 8. 后续可扩展方向

- 接入更多模型（Claude、GPT-4）对比效果
- 支持多浏览器测试（Firefox、Edge）
- 集成 CI 流程自动触发
- 支持复杂交互流程（拖拽、文件上传等）

---

## 9. 附录：用例 JSON 结构规范

字段说明：
- `test_name`：用例名称
- `steps`：步骤数组
  - `action`: click/type/assert 等
  - `selector`: CSS 选择器
  - `value`: 输入值（type）
  - `text`: 断言文本（assert）

更多扩展字段支持：等待时间、条件判断、异常处理等

---

如需更多文档，如 API Swagger 文档、AutoGen 配置样例或 MidSceneJS 执行脚本模板，请联系开发负责人。

---

