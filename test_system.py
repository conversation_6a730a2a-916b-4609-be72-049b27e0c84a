#!/usr/bin/env python3
"""
AI UI 自动化测试系统 - 系统测试脚本
用于验证系统各个组件的功能
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path

# 添加后端路径到 Python 路径
sys.path.append(str(Path(__file__).parent / "backend"))

from backend.services.browser_service import BrowserService
from backend.services.ai_service import AIService
from backend.services.test_executor import TestExecutor
from backend.services.task_manager import TaskManager
from backend.schemas.test_schemas import TestCase, TestStep, ActionType

class SystemTester:
    def __init__(self):
        self.browser_service = BrowserService()
        self.test_executor = TestExecutor()
        self.task_manager = TaskManager()
        
        # 只有在设置了 API Key 时才初始化 AI 服务
        self.ai_service = None
        if os.getenv("GEMINI_API_KEY"):
            try:
                self.ai_service = AIService()
            except Exception as e:
                print(f"⚠️  AI 服务初始化失败: {e}")
        
        self.test_results = []
    
    async def test_browser_service(self):
        """测试浏览器服务"""
        print("🔍 测试浏览器服务...")
        
        try:
            # 测试页面信息获取
            test_url = "https://example.com"
            page_info = await self.browser_service.capture_page_info(test_url)
            
            # 验证结果
            assert "screenshot_path" in page_info
            assert "dom_data" in page_info
            assert "title" in page_info
            assert os.path.exists(page_info["screenshot_path"])
            
            print("✅ 浏览器服务测试通过")
            self.test_results.append(("浏览器服务", True, "成功获取页面信息"))
            
            return page_info
            
        except Exception as e:
            print(f"❌ 浏览器服务测试失败: {e}")
            self.test_results.append(("浏览器服务", False, str(e)))
            return None
    
    async def test_ai_service(self, page_info):
        """测试 AI 服务"""
        print("🤖 测试 AI 服务...")
        
        if not self.ai_service:
            print("⚠️  跳过 AI 服务测试 (未设置 GEMINI_API_KEY)")
            self.test_results.append(("AI 服务", False, "未设置 API Key"))
            return self.create_mock_test_cases()
        
        try:
            # 生成测试用例
            test_cases = await self.ai_service.generate_test_cases(
                url=page_info["url"],
                screenshot_path=page_info["screenshot_path"],
                dom_data=page_info["dom_data"],
                user_requirements="生成基本的页面交互测试"
            )
            
            # 验证结果
            assert len(test_cases) > 0
            assert all(isinstance(case, TestCase) for case in test_cases)
            
            print(f"✅ AI 服务测试通过 (生成了 {len(test_cases)} 个测试用例)")
            self.test_results.append(("AI 服务", True, f"生成了 {len(test_cases)} 个测试用例"))
            
            return test_cases
            
        except Exception as e:
            print(f"❌ AI 服务测试失败: {e}")
            self.test_results.append(("AI 服务", False, str(e)))
            return self.create_mock_test_cases()
    
    def create_mock_test_cases(self):
        """创建模拟测试用例"""
        print("📝 创建模拟测试用例...")
        
        mock_case = TestCase(
            test_name="模拟测试用例",
            description="用于测试系统功能的模拟用例",
            steps=[
                TestStep(
                    action=ActionType.WAIT,
                    timeout=2000,
                    description="等待页面加载"
                ),
                TestStep(
                    action=ActionType.ASSERT,
                    selector="h1",
                    text="Example Domain",
                    description="验证页面标题"
                )
            ]
        )
        
        return [mock_case]
    
    async def test_test_executor(self, test_cases):
        """测试测试执行器"""
        print("⚡ 测试测试执行器...")
        
        try:
            # 执行第一个测试用例
            test_case = test_cases[0]
            result = await self.test_executor.execute_test_case(
                test_case=test_case,
                url="https://example.com",
                headless=True,
                task_id="test-123"
            )
            
            # 验证结果
            assert result.task_id == "test-123"
            assert result.test_name == test_case.test_name
            assert result.total_steps == len(test_case.steps)
            assert len(result.step_results) == len(test_case.steps)
            
            print(f"✅ 测试执行器测试通过 (执行了 {result.total_steps} 个步骤)")
            self.test_results.append(("测试执行器", True, f"成功执行 {result.total_steps} 个步骤"))
            
            return result
            
        except Exception as e:
            print(f"❌ 测试执行器测试失败: {e}")
            self.test_results.append(("测试执行器", False, str(e)))
            return None
    
    def test_task_manager(self):
        """测试任务管理器"""
        print("📋 测试任务管理器...")
        
        try:
            # 创建任务
            task_id = "test-task-123"
            self.task_manager.create_task(task_id, "processing", "测试任务")
            
            # 获取任务状态
            status = self.task_manager.get_task_status(task_id)
            assert status is not None
            assert status.task_id == task_id
            assert status.status == "processing"
            
            # 更新任务
            self.task_manager.update_task(task_id, "completed", "任务完成", 100.0)
            
            # 验证更新
            updated_status = self.task_manager.get_task_status(task_id)
            assert updated_status.status == "completed"
            assert updated_status.progress == 100.0
            
            # 设置和获取任务数据
            test_data = {"test": "data", "number": 123}
            self.task_manager.set_task_data(task_id, test_data)
            
            retrieved_data = self.task_manager.get_task_data(task_id)
            assert retrieved_data == test_data
            
            # 清理
            self.task_manager.delete_task(task_id)
            
            print("✅ 任务管理器测试通过")
            self.test_results.append(("任务管理器", True, "所有功能正常"))
            
        except Exception as e:
            print(f"❌ 任务管理器测试失败: {e}")
            self.test_results.append(("任务管理器", False, str(e)))
    
    def test_file_structure(self):
        """测试文件结构"""
        print("📁 测试文件结构...")
        
        required_dirs = [
            "backend",
            "frontend", 
            "scripts",
            "assets/screenshots",
            "assets/reports",
            "assets/tasks"
        ]
        
        required_files = [
            "backend/main.py",
            "backend/requirements.txt",
            "frontend/package.json",
            "scripts/test_executor.js",
            "README.md"
        ]
        
        missing_dirs = []
        missing_files = []
        
        # 检查目录
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)
        
        # 检查文件
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_dirs or missing_files:
            error_msg = f"缺少目录: {missing_dirs}, 缺少文件: {missing_files}"
            print(f"❌ 文件结构测试失败: {error_msg}")
            self.test_results.append(("文件结构", False, error_msg))
        else:
            print("✅ 文件结构测试通过")
            self.test_results.append(("文件结构", True, "所有必需文件和目录存在"))
    
    def print_test_summary(self):
        """打印测试摘要"""
        print("\n" + "="*50)
        print("📊 测试摘要")
        print("="*50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for _, passed, _ in self.test_results if passed)
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\n详细结果:")
        for test_name, passed, message in self.test_results:
            status = "✅" if passed else "❌"
            print(f"  {status} {test_name}: {message}")
        
        if failed_tests == 0:
            print(f"\n🎉 所有测试通过！系统可以正常使用。")
        else:
            print(f"\n⚠️  有 {failed_tests} 个测试失败，请检查相关组件。")
        
        return failed_tests == 0

async def main():
    """主函数"""
    print("🚀 AI UI 自动化测试系统 - 系统测试")
    print("="*50)
    
    # 检查环境变量
    if not os.getenv("GEMINI_API_KEY"):
        print("⚠️  未设置 GEMINI_API_KEY 环境变量，AI 功能将被跳过")
    
    tester = SystemTester()
    
    # 运行测试
    print("开始系统测试...\n")
    
    # 1. 测试文件结构
    tester.test_file_structure()
    
    # 2. 测试任务管理器
    tester.test_task_manager()
    
    # 3. 测试浏览器服务
    page_info = await tester.test_browser_service()
    
    # 4. 测试 AI 服务
    if page_info:
        test_cases = await tester.test_ai_service(page_info)
        
        # 5. 测试测试执行器
        if test_cases:
            await tester.test_test_executor(test_cases)
    
    # 打印测试摘要
    success = tester.print_test_summary()
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
