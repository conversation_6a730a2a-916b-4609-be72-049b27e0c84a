#!/bin/bash

# AI UI 自动化测试系统启动脚本

set -e

echo "🚀 AI UI 自动化测试系统启动脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}错误: $1 未安装${NC}"
        return 1
    fi
}

# 检查环境
check_environment() {
    echo -e "${BLUE}检查环境依赖...${NC}"
    
    # 检查 Python
    if check_command python3; then
        echo -e "${GREEN}✓ Python3 已安装${NC}"
    else
        echo -e "${RED}✗ 请安装 Python 3.10+${NC}"
        exit 1
    fi
    
    # 检查 Node.js
    if check_command node; then
        echo -e "${GREEN}✓ Node.js 已安装${NC}"
    else
        echo -e "${RED}✗ 请安装 Node.js 16+${NC}"
        exit 1
    fi
    
    # 检查 npm
    if check_command npm; then
        echo -e "${GREEN}✓ npm 已安装${NC}"
    else
        echo -e "${RED}✗ 请安装 npm${NC}"
        exit 1
    fi
}

# 设置后端
setup_backend() {
    echo -e "${BLUE}设置后端环境...${NC}"
    
    cd backend
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}创建 Python 虚拟环境...${NC}"
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    echo -e "${YELLOW}安装 Python 依赖...${NC}"
    pip install -r requirements.txt
    
    # 检查环境变量文件
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}创建环境变量文件...${NC}"
        cp .env.example .env
        echo -e "${RED}请编辑 backend/.env 文件，设置 GEMINI_API_KEY${NC}"
    fi
    
    cd ..
    echo -e "${GREEN}✓ 后端环境设置完成${NC}"
}

# 设置前端
setup_frontend() {
    echo -e "${BLUE}设置前端环境...${NC}"
    
    cd frontend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装前端依赖...${NC}"
        npm install
    fi
    
    cd ..
    echo -e "${GREEN}✓ 前端环境设置完成${NC}"
}

# 设置脚本环境
setup_scripts() {
    echo -e "${BLUE}设置脚本环境...${NC}"
    
    cd scripts
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装脚本依赖...${NC}"
        npm install
    fi
    
    # 安装浏览器
    echo -e "${YELLOW}安装 Playwright 浏览器...${NC}"
    npx playwright install chromium
    
    cd ..
    echo -e "${GREEN}✓ 脚本环境设置完成${NC}"
}

# 创建必要目录
create_directories() {
    echo -e "${BLUE}创建必要目录...${NC}"
    
    mkdir -p assets/screenshots
    mkdir -p assets/reports
    mkdir -p assets/tasks
    
    echo -e "${GREEN}✓ 目录创建完成${NC}"
}

# 启动后端
start_backend() {
    echo -e "${BLUE}启动后端服务...${NC}"
    
    cd backend
    source venv/bin/activate
    
    # 检查环境变量
    if [ ! -f ".env" ] || ! grep -q "GEMINI_API_KEY=" .env; then
        echo -e "${RED}错误: 请在 backend/.env 文件中设置 GEMINI_API_KEY${NC}"
        exit 1
    fi
    
    python main.py &
    BACKEND_PID=$!
    
    cd ..
    echo -e "${GREEN}✓ 后端服务已启动 (PID: $BACKEND_PID)${NC}"
    
    # 等待后端启动
    echo -e "${YELLOW}等待后端服务启动...${NC}"
    sleep 5
    
    # 检查后端是否启动成功
    if curl -s http://localhost:8000/health > /dev/null; then
        echo -e "${GREEN}✓ 后端服务启动成功${NC}"
    else
        echo -e "${RED}✗ 后端服务启动失败${NC}"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
}

# 启动前端
start_frontend() {
    echo -e "${BLUE}启动前端服务...${NC}"
    
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
    echo -e "${GREEN}✓ 前端服务已启动 (PID: $FRONTEND_PID)${NC}"
}

# 显示服务信息
show_services() {
    echo ""
    echo -e "${GREEN}🎉 系统启动完成！${NC}"
    echo "=================================="
    echo -e "${BLUE}服务地址:${NC}"
    echo "  前端界面: http://localhost:3000"
    echo "  后端 API: http://localhost:8000"
    echo "  API 文档: http://localhost:8000/docs"
    echo ""
    echo -e "${YELLOW}使用说明:${NC}"
    echo "  1. 打开浏览器访问 http://localhost:3000"
    echo "  2. 输入要测试的网站 URL"
    echo "  3. 等待 AI 分析并生成测试用例"
    echo "  4. 执行测试并查看报告"
    echo ""
    echo -e "${YELLOW}停止服务: Ctrl+C${NC}"
}

# 清理函数
cleanup() {
    echo ""
    echo -e "${YELLOW}正在停止服务...${NC}"
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo -e "${GREEN}✓ 后端服务已停止${NC}"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo -e "${GREEN}✓ 前端服务已停止${NC}"
    fi
    
    echo -e "${GREEN}✓ 所有服务已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    case "${1:-start}" in
        "setup")
            check_environment
            setup_backend
            setup_frontend
            setup_scripts
            create_directories
            echo -e "${GREEN}🎉 环境设置完成！运行 './start.sh' 启动系统${NC}"
            ;;
        "start")
            check_environment
            create_directories
            start_backend
            start_frontend
            show_services
            
            # 保持脚本运行
            while true; do
                sleep 1
            done
            ;;
        "dev")
            echo -e "${BLUE}开发模式启动...${NC}"
            check_environment
            create_directories
            
            # 只启动后端，前端需要手动启动
            start_backend
            
            echo ""
            echo -e "${GREEN}🎉 开发环境启动完成！${NC}"
            echo "=================================="
            echo -e "${BLUE}后端服务:${NC} http://localhost:8000"
            echo -e "${YELLOW}请手动启动前端:${NC} cd frontend && npm run dev"
            
            # 保持脚本运行
            while true; do
                sleep 1
            done
            ;;
        "help"|"-h"|"--help")
            echo "AI UI 自动化测试系统启动脚本"
            echo ""
            echo "用法:"
            echo "  ./start.sh [命令]"
            echo ""
            echo "命令:"
            echo "  setup    - 初始化环境和依赖"
            echo "  start    - 启动完整系统 (默认)"
            echo "  dev      - 开发模式 (仅启动后端)"
            echo "  help     - 显示帮助信息"
            echo ""
            echo "示例:"
            echo "  ./start.sh setup   # 首次运行，设置环境"
            echo "  ./start.sh start   # 启动系统"
            echo "  ./start.sh dev     # 开发模式"
            ;;
        *)
            echo -e "${RED}错误: 未知命令 '$1'${NC}"
            echo "运行 './start.sh help' 查看帮助"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
