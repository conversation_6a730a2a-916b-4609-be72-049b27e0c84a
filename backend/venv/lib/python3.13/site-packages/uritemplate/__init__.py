"""

uritemplate
===========

URI templates implemented as close to :rfc:`6570` as possible

See http://uritemplate.rtfd.org/ for documentation

:copyright:
    (c) 2013 <PERSON>
:license:
    Modified BSD Apache License (Version 2.0), see LICENSE for more details
    and either LICENSE.BSD or LICENSE.APACHE for the details of those specific
    licenses

"""

__title__ = "uritemplate"
__author__ = "<PERSON>"
__license__ = "Modified BSD or Apache License, Version 2.0"
__copyright__ = "Copyright 2013 Ian <PERSON> Corda<PERSON>"
__version__ = "4.2.0"
__version_info__ = tuple(
    int(i) for i in __version__.split(".") if i.isdigit()
)

from uritemplate.api import URITemplate
from uritemplate.api import expand
from uritemplate.api import partial
from uritemplate.api import variables

__all__ = ("URITemplate", "expand", "partial", "variables")
