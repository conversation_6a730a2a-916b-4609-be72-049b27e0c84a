"""
测试相关的数据模型定义
"""
from pydantic import BaseModel, HttpUrl
from typing import List, Optional, Dict, Any
from enum import Enum

class ActionType(str, Enum):
    CLICK = "click"
    TYPE = "type"
    ASSERT = "assert"
    WAIT = "wait"
    NAVIGATE = "navigate"
    SCROLL = "scroll"

class TestStep(BaseModel):
    action: ActionType
    selector: Optional[str] = None
    value: Optional[str] = None
    text: Optional[str] = None
    timeout: Optional[int] = 5000  # 默认5秒超时
    description: Optional[str] = None

class TestCase(BaseModel):
    test_name: str
    description: Optional[str] = None
    steps: List[TestStep]
    expected_duration: Optional[int] = None  # 预期执行时间（秒）

class UrlSubmissionRequest(BaseModel):
    url: HttpUrl
    description: Optional[str] = None

class TestCaseGenerationRequest(BaseModel):
    url: str
    screenshot_path: str
    dom_data: str
    user_requirements: Optional[str] = None

class TestExecutionRequest(BaseModel):
    test_case: TestCase
    url: str
    headless: bool = False

class StepResult(BaseModel):
    step_index: int
    action: ActionType
    selector: Optional[str]
    status: str  # "success", "failed", "skipped"
    message: str
    screenshot_path: Optional[str] = None
    execution_time: float  # 执行时间（秒）
    error_details: Optional[str] = None

class TestExecutionResult(BaseModel):
    task_id: str
    test_name: str
    status: str  # "running", "completed", "failed"
    start_time: str
    end_time: Optional[str] = None
    total_steps: int
    passed_steps: int
    failed_steps: int
    step_results: List[StepResult]
    final_screenshot: Optional[str] = None
    execution_log: List[str] = []

class TaskStatus(BaseModel):
    task_id: str
    status: str
    progress: float  # 0-100
    message: str
    created_at: str
    updated_at: str
