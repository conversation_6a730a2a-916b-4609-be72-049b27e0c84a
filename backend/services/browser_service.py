"""
浏览器控制服务 - 使用 MidSceneJS 控制 Chrome
"""
import asyncio
import subprocess
import json
import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
import tempfile

class BrowserService:
    def __init__(self):
        self.screenshots_dir = "assets/screenshots"
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    async def capture_page_info(self, url: str) -> Dict[str, Any]:
        """
        使用 MidSceneJS 获取页面截图和 DOM 信息
        """
        try:
            # 生成唯一的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            task_id = str(uuid.uuid4())[:8]
            screenshot_filename = f"page_{timestamp}_{task_id}.png"
            screenshot_path = os.path.join(self.screenshots_dir, screenshot_filename)
            
            # 创建临时的 JavaScript 脚本
            script_content = self._generate_capture_script(url, screenshot_path)
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(script_content)
                script_path = f.name
            
            try:
                # 执行 MidSceneJS 脚本
                result = await self._run_midscene_script(script_path)
                
                # 解析结果
                if result["success"]:
                    return {
                        "screenshot_path": screenshot_path,
                        "dom_data": result["dom_data"],
                        "title": result["title"],
                        "url": url,
                        "captured_at": datetime.now().isoformat()
                    }
                else:
                    raise Exception(f"MidSceneJS 执行失败: {result['error']}")
                    
            finally:
                # 清理临时文件
                if os.path.exists(script_path):
                    os.unlink(script_path)
                    
        except Exception as e:
            raise Exception(f"获取页面信息失败: {str(e)}")
    
    def _generate_capture_script(self, url: str, screenshot_path: str) -> str:
        """
        生成用于页面信息获取的 MidSceneJS 脚本
        """
        return f"""
const {{ chromium }} = require('playwright');
const fs = require('fs');

(async () => {{
    let browser;
    try {{
        browser = await chromium.launch({{ headless: true }});
        const context = await browser.newContext({{
            viewport: {{ width: 1920, height: 1080 }}
        }});
        const page = await context.newPage();
        
        // 导航到页面
        await page.goto('{url}', {{ waitUntil: 'networkidle' }});
        
        // 等待页面加载完成
        await page.waitForTimeout(2000);
        
        // 获取页面标题
        const title = await page.title();
        
        // 获取 DOM 结构（简化版）
        const domData = await page.evaluate(() => {{
            const getElementInfo = (element) => {{
                if (!element || element.nodeType !== 1) return null;
                
                const rect = element.getBoundingClientRect();
                const styles = window.getComputedStyle(element);
                
                return {{
                    tagName: element.tagName.toLowerCase(),
                    id: element.id || null,
                    className: element.className || null,
                    text: element.textContent?.trim().substring(0, 100) || null,
                    visible: styles.display !== 'none' && styles.visibility !== 'hidden',
                    rect: {{
                        x: Math.round(rect.x),
                        y: Math.round(rect.y),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    }},
                    attributes: Array.from(element.attributes).reduce((acc, attr) => {{
                        acc[attr.name] = attr.value;
                        return acc;
                    }}, {{}})
                }};
            }};
            
            const interactiveElements = [];
            const selectors = [
                'button', 'input', 'select', 'textarea', 'a[href]',
                '[onclick]', '[role="button"]', '[tabindex]'
            ];
            
            selectors.forEach(selector => {{
                const elements = document.querySelectorAll(selector);
                elements.forEach(el => {{
                    const info = getElementInfo(el);
                    if (info && info.visible && info.rect.width > 0 && info.rect.height > 0) {{
                        interactiveElements.push(info);
                    }}
                }});
            }});
            
            return {{
                title: document.title,
                url: window.location.href,
                interactiveElements: interactiveElements.slice(0, 50), // 限制数量
                forms: Array.from(document.forms).map(form => getElementInfo(form)).filter(Boolean)
            }};
        }});
        
        // 截图
        await page.screenshot({{ 
            path: '{screenshot_path}',
            fullPage: true,
            type: 'png'
        }});
        
        console.log(JSON.stringify({{
            success: true,
            title: title,
            dom_data: JSON.stringify(domData),
            screenshot_path: '{screenshot_path}'
        }}));
        
    }} catch (error) {{
        console.log(JSON.stringify({{
            success: false,
            error: error.message
        }}));
    }} finally {{
        if (browser) {{
            await browser.close();
        }}
    }}
}})();
"""
    
    async def _run_midscene_script(self, script_path: str) -> Dict[str, Any]:
        """
        执行 MidSceneJS 脚本
        """
        try:
            # 使用 Node.js 执行脚本
            process = await asyncio.create_subprocess_exec(
                'node', script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 解析输出
                output = stdout.decode('utf-8').strip()
                return json.loads(output)
            else:
                error_msg = stderr.decode('utf-8') if stderr else "未知错误"
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def cleanup_screenshots(self, older_than_days: int = 7):
        """
        清理旧的截图文件
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (older_than_days * 24 * 60 * 60)
            
            for filename in os.listdir(self.screenshots_dir):
                file_path = os.path.join(self.screenshots_dir, filename)
                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        
        except Exception as e:
            print(f"清理截图文件失败: {e}")
