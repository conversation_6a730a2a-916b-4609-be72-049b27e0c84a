"""
报告服务 - 生成测试报告和统计信息
"""
import os
import json
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, List
import tempfile

class ReportService:
    def __init__(self):
        self.reports_dir = "assets/reports"
        os.makedirs(self.reports_dir, exist_ok=True)
    
    async def generate_detailed_report(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成详细的测试报告
        """
        try:
            report = {
                "report_id": task_data.get("task_id"),
                "test_name": task_data.get("test_name", "未知测试"),
                "generated_at": datetime.now().isoformat(),
                "summary": self._generate_summary(task_data),
                "execution_details": self._generate_execution_details(task_data),
                "step_analysis": self._generate_step_analysis(task_data),
                "recommendations": self._generate_recommendations(task_data)
            }
            
            # 保存报告
            report_file = os.path.join(self.reports_dir, f"{report['report_id']}_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            return report
            
        except Exception as e:
            raise Exception(f"生成详细报告失败: {str(e)}")
    
    def _generate_summary(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成测试摘要
        """
        summary = {
            "total_steps": task_data.get("total_steps", 0),
            "passed_steps": task_data.get("passed_steps", 0),
            "failed_steps": task_data.get("failed_steps", 0),
            "success_rate": 0.0,
            "execution_time": 0.0,
            "status": task_data.get("status", "unknown")
        }
        
        if summary["total_steps"] > 0:
            summary["success_rate"] = (summary["passed_steps"] / summary["total_steps"]) * 100
        
        # 计算总执行时间
        step_results = task_data.get("step_results", [])
        summary["execution_time"] = sum(step.get("execution_time", 0) for step in step_results)
        
        return summary
    
    def _generate_execution_details(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成执行详情
        """
        details = {
            "start_time": task_data.get("start_time"),
            "end_time": task_data.get("end_time"),
            "target_url": task_data.get("url"),
            "execution_log": task_data.get("execution_log", []),
            "final_screenshot": task_data.get("final_screenshot"),
            "environment": {
                "browser": "Chrome (Playwright)",
                "headless": True,
                "viewport": "1920x1080"
            }
        }
        
        return details
    
    def _generate_step_analysis(self, task_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成步骤分析
        """
        step_results = task_data.get("step_results", [])
        analysis = []
        
        for step in step_results:
            step_analysis = {
                "step_index": step.get("step_index"),
                "action": step.get("action"),
                "selector": step.get("selector"),
                "status": step.get("status"),
                "execution_time": step.get("execution_time", 0),
                "message": step.get("message"),
                "screenshot_path": step.get("screenshot_path"),
                "performance_rating": self._rate_step_performance(step),
                "issues": self._identify_step_issues(step)
            }
            analysis.append(step_analysis)
        
        return analysis
    
    def _generate_recommendations(self, task_data: Dict[str, Any]) -> List[str]:
        """
        生成改进建议
        """
        recommendations = []
        step_results = task_data.get("step_results", [])
        
        # 分析失败步骤
        failed_steps = [step for step in step_results if step.get("status") == "failed"]
        if failed_steps:
            recommendations.append(f"发现 {len(failed_steps)} 个失败步骤，建议检查选择器的准确性")
        
        # 分析执行时间
        slow_steps = [step for step in step_results if step.get("execution_time", 0) > 10]
        if slow_steps:
            recommendations.append(f"发现 {len(slow_steps)} 个执行缓慢的步骤，建议优化等待时间")
        
        # 分析成功率
        total_steps = len(step_results)
        passed_steps = len([step for step in step_results if step.get("status") == "success"])
        
        if total_steps > 0:
            success_rate = (passed_steps / total_steps) * 100
            if success_rate < 80:
                recommendations.append("测试成功率较低，建议重新分析页面结构并优化测试用例")
            elif success_rate == 100:
                recommendations.append("所有测试步骤执行成功，测试用例质量良好")
        
        if not recommendations:
            recommendations.append("测试执行正常，暂无特殊建议")
        
        return recommendations
    
    def _rate_step_performance(self, step: Dict[str, Any]) -> str:
        """
        评估步骤性能
        """
        execution_time = step.get("execution_time", 0)
        status = step.get("status")
        
        if status == "failed":
            return "差"
        elif execution_time > 10:
            return "慢"
        elif execution_time < 2:
            return "优秀"
        else:
            return "良好"
    
    def _identify_step_issues(self, step: Dict[str, Any]) -> List[str]:
        """
        识别步骤问题
        """
        issues = []
        
        if step.get("status") == "failed":
            error_details = step.get("error_details", "")
            if "timeout" in error_details.lower():
                issues.append("超时错误")
            elif "selector" in error_details.lower():
                issues.append("选择器问题")
            else:
                issues.append("执行错误")
        
        execution_time = step.get("execution_time", 0)
        if execution_time > 10:
            issues.append("执行时间过长")
        
        return issues
    
    async def get_test_statistics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """
        获取测试统计信息
        """
        try:
            stats = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "total_tests": 0,
                "successful_tests": 0,
                "failed_tests": 0,
                "average_success_rate": 0.0,
                "total_execution_time": 0.0,
                "most_common_failures": [],
                "performance_trends": []
            }
            
            # 扫描报告文件
            report_files = [f for f in os.listdir(self.reports_dir) if f.endswith('_report.json')]
            
            test_data = []
            for report_file in report_files:
                try:
                    with open(os.path.join(self.reports_dir, report_file), 'r', encoding='utf-8') as f:
                        report = json.load(f)
                    
                    # 检查日期范围
                    generated_at = datetime.fromisoformat(report.get("generated_at", ""))
                    if start_date <= generated_at <= end_date:
                        test_data.append(report)
                        
                except Exception as e:
                    print(f"处理报告文件 {report_file} 失败: {e}")
                    continue
            
            # 计算统计信息
            stats["total_tests"] = len(test_data)
            
            if test_data:
                successful_tests = sum(1 for test in test_data if test.get("summary", {}).get("success_rate", 0) == 100)
                stats["successful_tests"] = successful_tests
                stats["failed_tests"] = stats["total_tests"] - successful_tests
                
                # 平均成功率
                success_rates = [test.get("summary", {}).get("success_rate", 0) for test in test_data]
                stats["average_success_rate"] = sum(success_rates) / len(success_rates)
                
                # 总执行时间
                execution_times = [test.get("summary", {}).get("execution_time", 0) for test in test_data]
                stats["total_execution_time"] = sum(execution_times)
                
                # 常见失败原因
                stats["most_common_failures"] = self._analyze_common_failures(test_data)
            
            return stats
            
        except Exception as e:
            raise Exception(f"获取统计信息失败: {str(e)}")
    
    def _analyze_common_failures(self, test_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        分析常见失败原因
        """
        failure_counts = {}
        
        for test in test_data:
            step_analysis = test.get("step_analysis", [])
            for step in step_analysis:
                issues = step.get("issues", [])
                for issue in issues:
                    failure_counts[issue] = failure_counts.get(issue, 0) + 1
        
        # 排序并返回前5个
        sorted_failures = sorted(failure_counts.items(), key=lambda x: x[1], reverse=True)
        
        return [
            {"issue": issue, "count": count}
            for issue, count in sorted_failures[:5]
        ]
    
    async def export_report(self, task_data: Dict[str, Any], format: str) -> Dict[str, Any]:
        """
        导出报告
        """
        try:
            task_id = task_data.get("task_id", "unknown")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if format == "json":
                return await self._export_json_report(task_data, task_id, timestamp)
            elif format == "html":
                return await self._export_html_report(task_data, task_id, timestamp)
            elif format == "pdf":
                return await self._export_pdf_report(task_data, task_id, timestamp)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            raise Exception(f"导出报告失败: {str(e)}")
    
    async def _export_json_report(self, task_data: Dict[str, Any], task_id: str, timestamp: str) -> Dict[str, Any]:
        """
        导出 JSON 格式报告
        """
        filename = f"report_{task_id}_{timestamp}.json"
        file_path = os.path.join(self.reports_dir, filename)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(task_data, f, ensure_ascii=False, indent=2)
        
        file_size = os.path.getsize(file_path)
        
        return {
            "download_url": f"/assets/reports/{filename}",
            "file_name": filename,
            "file_size": file_size
        }
    
    async def _export_html_report(self, task_data: Dict[str, Any], task_id: str, timestamp: str) -> Dict[str, Any]:
        """
        导出 HTML 格式报告
        """
        # 简化的 HTML 报告生成
        filename = f"report_{task_id}_{timestamp}.html"
        file_path = os.path.join(self.reports_dir, filename)
        
        html_content = self._generate_html_report(task_data)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        file_size = os.path.getsize(file_path)
        
        return {
            "download_url": f"/assets/reports/{filename}",
            "file_name": filename,
            "file_size": file_size
        }
    
    def _generate_html_report(self, task_data: Dict[str, Any]) -> str:
        """
        生成 HTML 报告内容
        """
        # 简化的 HTML 模板
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>测试报告 - {task_data.get('test_name', '未知测试')}</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .step {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; }}
        .success {{ background: #d4edda; }}
        .failed {{ background: #f8d7da; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>测试报告</h1>
        <p>测试名称: {task_data.get('test_name', '未知测试')}</p>
        <p>执行时间: {task_data.get('start_time', '未知')}</p>
    </div>
    
    <div class="summary">
        <h2>执行摘要</h2>
        <p>总步骤: {task_data.get('total_steps', 0)}</p>
        <p>成功步骤: {task_data.get('passed_steps', 0)}</p>
        <p>失败步骤: {task_data.get('failed_steps', 0)}</p>
    </div>
    
    <div class="steps">
        <h2>步骤详情</h2>
        {''.join(self._generate_step_html(step) for step in task_data.get('step_results', []))}
    </div>
</body>
</html>
"""
    
    def _generate_step_html(self, step: Dict[str, Any]) -> str:
        """
        生成步骤 HTML
        """
        status_class = "success" if step.get("status") == "success" else "failed"
        return f"""
        <div class="step {status_class}">
            <h3>步骤 {step.get('step_index', 0) + 1}: {step.get('action', '未知操作')}</h3>
            <p>选择器: {step.get('selector', '无')}</p>
            <p>状态: {step.get('status', '未知')}</p>
            <p>消息: {step.get('message', '无')}</p>
            <p>执行时间: {step.get('execution_time', 0):.2f}秒</p>
        </div>
        """
    
    async def _export_pdf_report(self, task_data: Dict[str, Any], task_id: str, timestamp: str) -> Dict[str, Any]:
        """
        导出 PDF 格式报告（简化实现）
        """
        # 这里可以使用 reportlab 或其他 PDF 生成库
        # 为了简化，我们先生成 HTML 然后提示用户可以打印为 PDF
        return await self._export_html_report(task_data, task_id, timestamp)
    
    async def cleanup_task_files(self, task_id: str):
        """
        清理任务相关文件
        """
        try:
            # 清理报告文件
            report_file = os.path.join(self.reports_dir, f"{task_id}_report.json")
            if os.path.exists(report_file):
                os.remove(report_file)
            
            # 清理截图文件
            screenshots_dir = "assets/screenshots"
            if os.path.exists(screenshots_dir):
                for filename in os.listdir(screenshots_dir):
                    if task_id in filename:
                        file_path = os.path.join(screenshots_dir, filename)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                            
        except Exception as e:
            print(f"清理任务文件失败: {e}")
