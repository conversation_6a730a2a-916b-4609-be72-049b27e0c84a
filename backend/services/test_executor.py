"""
测试执行器 - 使用 MidSceneJS 执行测试用例
"""
import asyncio
import json
import os
import uuid
import tempfile
from datetime import datetime
from typing import Dict, Any, List

from schemas.test_schemas import TestCase, TestExecutionResult, StepResult, ActionType

class TestExecutor:
    def __init__(self):
        self.screenshots_dir = "assets/screenshots"
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    async def execute_test_case(
        self, 
        test_case: TestCase, 
        url: str, 
        headless: bool = False,
        task_id: str = None
    ) -> TestExecutionResult:
        """
        执行测试用例
        """
        if not task_id:
            task_id = str(uuid.uuid4())
        
        start_time = datetime.now()
        execution_log = []
        step_results = []
        
        try:
            execution_log.append(f"开始执行测试用例: {test_case.test_name}")
            
            # 生成执行脚本
            script_content = self._generate_execution_script(
                test_case, url, headless, task_id
            )
            
            # 创建临时脚本文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(script_content)
                script_path = f.name
            
            try:
                # 执行脚本
                result = await self._run_execution_script(script_path)
                
                if result["success"]:
                    step_results = self._parse_step_results(result["step_results"])
                    execution_log.extend(result.get("logs", []))
                else:
                    execution_log.append(f"执行失败: {result['error']}")
                    
            finally:
                # 清理临时文件
                if os.path.exists(script_path):
                    os.unlink(script_path)
            
            # 统计结果
            total_steps = len(test_case.steps)
            passed_steps = sum(1 for r in step_results if r.status == "success")
            failed_steps = total_steps - passed_steps
            
            # 确定整体状态
            overall_status = "completed" if failed_steps == 0 else "failed"
            
            return TestExecutionResult(
                task_id=task_id,
                test_name=test_case.test_name,
                status=overall_status,
                start_time=start_time.isoformat(),
                end_time=datetime.now().isoformat(),
                total_steps=total_steps,
                passed_steps=passed_steps,
                failed_steps=failed_steps,
                step_results=step_results,
                final_screenshot=result.get("final_screenshot") if result.get("success") else None,
                execution_log=execution_log
            )
            
        except Exception as e:
            execution_log.append(f"执行异常: {str(e)}")
            
            return TestExecutionResult(
                task_id=task_id,
                test_name=test_case.test_name,
                status="failed",
                start_time=start_time.isoformat(),
                end_time=datetime.now().isoformat(),
                total_steps=len(test_case.steps),
                passed_steps=0,
                failed_steps=len(test_case.steps),
                step_results=[],
                execution_log=execution_log
            )
    
    def _generate_execution_script(
        self, 
        test_case: TestCase, 
        url: str, 
        headless: bool,
        task_id: str
    ) -> str:
        """
        生成测试执行脚本
        """
        steps_json = json.dumps([step.dict() for step in test_case.steps], ensure_ascii=False)
        
        return f"""
const {{ chromium }} = require('playwright');
const fs = require('fs');
const path = require('path');

const steps = {steps_json};
const screenshotsDir = '{self.screenshots_dir}';
const taskId = '{task_id}';

(async () => {{
    let browser;
    const stepResults = [];
    const logs = [];
    
    try {{
        browser = await chromium.launch({{ 
            headless: {str(headless).lower()},
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        }});
        
        const context = await browser.newContext({{
            viewport: {{ width: 1920, height: 1080 }}
        }});
        const page = await context.newPage();
        
        // 导航到页面
        logs.push(`导航到: {url}`);
        await page.goto('{url}', {{ waitUntil: 'networkidle' }});
        await page.waitForTimeout(2000);
        
        // 执行每个步骤
        for (let i = 0; i < steps.length; i++) {{
            const step = steps[i];
            const stepStartTime = Date.now();
            let stepResult = {{
                step_index: i,
                action: step.action,
                selector: step.selector,
                status: 'success',
                message: '',
                screenshot_path: null,
                execution_time: 0,
                error_details: null
            }};
            
            try {{
                logs.push(`执行步骤 ${{i + 1}}: ${{step.action}} ${{step.selector || ''}}`);
                
                switch (step.action) {{
                    case 'click':
                        await page.click(step.selector, {{ timeout: step.timeout || 5000 }});
                        stepResult.message = `成功点击元素: ${{step.selector}}`;
                        break;
                        
                    case 'type':
                        await page.fill(step.selector, step.value || '', {{ timeout: step.timeout || 5000 }});
                        stepResult.message = `成功输入文本到: ${{step.selector}}`;
                        break;
                        
                    case 'assert':
                        const element = await page.locator(step.selector).first();
                        const text = await element.textContent();
                        if (text && text.includes(step.text)) {{
                            stepResult.message = `断言成功: 找到文本 "${{step.text}}"`;
                        }} else {{
                            throw new Error(`断言失败: 期望文本 "${{step.text}}", 实际文本 "${{text}}"`);
                        }}
                        break;
                        
                    case 'wait':
                        await page.waitForTimeout(step.timeout || 1000);
                        stepResult.message = `等待 ${{step.timeout || 1000}}ms`;
                        break;
                        
                    case 'navigate':
                        await page.goto(step.value, {{ waitUntil: 'networkidle' }});
                        stepResult.message = `导航到: ${{step.value}}`;
                        break;
                        
                    case 'scroll':
                        await page.evaluate(() => window.scrollBy(0, 500));
                        stepResult.message = '页面向下滚动';
                        break;
                        
                    default:
                        throw new Error(`不支持的操作: ${{step.action}}`);
                }}
                
                // 每步执行后截图
                const stepScreenshot = path.join(screenshotsDir, `${{taskId}}_step_${{i + 1}}.png`);
                await page.screenshot({{ path: stepScreenshot, type: 'png' }});
                stepResult.screenshot_path = stepScreenshot;
                
            }} catch (error) {{
                stepResult.status = 'failed';
                stepResult.message = `步骤执行失败: ${{error.message}}`;
                stepResult.error_details = error.stack;
                logs.push(`步骤 ${{i + 1}} 失败: ${{error.message}}`);
                
                // 失败时也截图
                try {{
                    const errorScreenshot = path.join(screenshotsDir, `${{taskId}}_error_step_${{i + 1}}.png`);
                    await page.screenshot({{ path: errorScreenshot, type: 'png' }});
                    stepResult.screenshot_path = errorScreenshot;
                }} catch (screenshotError) {{
                    logs.push(`截图失败: ${{screenshotError.message}}`);
                }}
            }}
            
            stepResult.execution_time = (Date.now() - stepStartTime) / 1000;
            stepResults.push(stepResult);
        }}
        
        // 最终截图
        const finalScreenshot = path.join(screenshotsDir, `${{taskId}}_final.png`);
        await page.screenshot({{ path: finalScreenshot, fullPage: true, type: 'png' }});
        
        console.log(JSON.stringify({{
            success: true,
            step_results: stepResults,
            final_screenshot: finalScreenshot,
            logs: logs
        }}));
        
    }} catch (error) {{
        console.log(JSON.stringify({{
            success: false,
            error: error.message,
            step_results: stepResults,
            logs: logs
        }}));
    }} finally {{
        if (browser) {{
            await browser.close();
        }}
    }}
}})();
"""
    
    async def _run_execution_script(self, script_path: str) -> Dict[str, Any]:
        """
        执行测试脚本
        """
        try:
            process = await asyncio.create_subprocess_exec(
                'node', script_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                output = stdout.decode('utf-8').strip()
                return json.loads(output)
            else:
                error_msg = stderr.decode('utf-8') if stderr else "未知错误"
                return {
                    "success": False,
                    "error": error_msg
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def _parse_step_results(self, step_results_data: List[Dict[str, Any]]) -> List[StepResult]:
        """
        解析步骤执行结果
        """
        results = []
        for data in step_results_data:
            result = StepResult(
                step_index=data["step_index"],
                action=ActionType(data["action"]),
                selector=data.get("selector"),
                status=data["status"],
                message=data["message"],
                screenshot_path=data.get("screenshot_path"),
                execution_time=data["execution_time"],
                error_details=data.get("error_details")
            )
            results.append(result)
        
        return results
