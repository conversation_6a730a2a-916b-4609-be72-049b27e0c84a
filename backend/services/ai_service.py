"""
AI 服务 - 使用 AutoGen + Gemini 生成测试用例
"""
import os
import json
import base64
from typing import List, Dict, Any, Optional
import google.generativeai as genai
from datetime import datetime

from schemas.test_schemas import TestCase, TestStep, ActionType

class AIService:
    def __init__(self):
        # 配置 Gemini API
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("请设置 GEMINI_API_KEY 环境变量")
        
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
    
    async def generate_test_cases(
        self, 
        url: str, 
        screenshot_path: str, 
        dom_data: str,
        user_requirements: Optional[str] = None
    ) -> List[TestCase]:
        """
        基于页面信息生成测试用例
        """
        try:
            # 读取截图
            screenshot_data = self._encode_image(screenshot_path)
            
            # 构建提示词
            prompt = self._build_test_generation_prompt(
                url, dom_data, user_requirements
            )
            
            # 调用 Gemini 生成测试用例
            response = await self._call_gemini_with_image(prompt, screenshot_data)
            
            # 解析响应并转换为 TestCase 对象
            test_cases = self._parse_test_cases_response(response)
            
            return test_cases
            
        except Exception as e:
            raise Exception(f"生成测试用例失败: {str(e)}")
    
    def _encode_image(self, image_path: str) -> str:
        """
        将图片编码为 base64
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise Exception(f"读取截图失败: {str(e)}")
    
    def _build_test_generation_prompt(
        self, 
        url: str, 
        dom_data: str, 
        user_requirements: Optional[str]
    ) -> str:
        """
        构建测试用例生成的提示词
        """
        base_prompt = f"""
你是一个专业的UI自动化测试专家。请基于提供的网页截图和DOM结构信息，生成全面的UI自动化测试用例。

网页信息：
- URL: {url}
- DOM结构: {dom_data}

请生成测试用例，包含以下类型的测试：
1. 基本功能测试（点击、输入、导航等）
2. 表单验证测试
3. 用户交互流程测试
4. 页面元素可见性测试

用户特殊要求：
{user_requirements if user_requirements else "无特殊要求，请生成标准的UI测试用例"}

请严格按照以下JSON格式返回测试用例数组：

```json
[
  {{
    "test_name": "测试用例名称",
    "description": "测试用例描述",
    "steps": [
      {{
        "action": "click|type|assert|wait|navigate|scroll",
        "selector": "CSS选择器",
        "value": "输入值（仅type动作需要）",
        "text": "断言文本（仅assert动作需要）",
        "timeout": 5000,
        "description": "步骤描述"
      }}
    ],
    "expected_duration": 30
  }}
]
```

注意事项：
1. 选择器必须基于提供的DOM结构中的实际元素
2. 测试步骤要符合实际的用户操作流程
3. 包含适当的等待和断言步骤
4. 每个测试用例应该是独立可执行的
5. 生成3-5个不同类型的测试用例
6. 确保JSON格式正确，可以被解析

请只返回JSON数组，不要包含其他文本。
"""
        return base_prompt
    
    async def _call_gemini_with_image(self, prompt: str, image_data: str) -> str:
        """
        调用 Gemini API 处理图片和文本
        """
        try:
            # 准备图片数据
            image_part = {
                "mime_type": "image/png",
                "data": image_data
            }
            
            # 调用模型
            response = self.model.generate_content([prompt, image_part])
            
            return response.text
            
        except Exception as e:
            raise Exception(f"调用 Gemini API 失败: {str(e)}")
    
    def _parse_test_cases_response(self, response: str) -> List[TestCase]:
        """
        解析 AI 响应并转换为 TestCase 对象
        """
        try:
            # 提取 JSON 部分
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("响应中未找到有效的JSON数组")
            
            json_str = response[json_start:json_end]
            test_cases_data = json.loads(json_str)
            
            # 转换为 TestCase 对象
            test_cases = []
            for case_data in test_cases_data:
                # 转换步骤
                steps = []
                for step_data in case_data.get("steps", []):
                    step = TestStep(
                        action=ActionType(step_data["action"]),
                        selector=step_data.get("selector"),
                        value=step_data.get("value"),
                        text=step_data.get("text"),
                        timeout=step_data.get("timeout", 5000),
                        description=step_data.get("description")
                    )
                    steps.append(step)
                
                # 创建测试用例
                test_case = TestCase(
                    test_name=case_data["test_name"],
                    description=case_data.get("description"),
                    steps=steps,
                    expected_duration=case_data.get("expected_duration")
                )
                test_cases.append(test_case)
            
            return test_cases
            
        except json.JSONDecodeError as e:
            raise Exception(f"解析AI响应JSON失败: {str(e)}")
        except Exception as e:
            raise Exception(f"处理AI响应失败: {str(e)}")
    
    async def optimize_test_case(self, test_case: TestCase, execution_result: Dict[str, Any]) -> TestCase:
        """
        基于执行结果优化测试用例
        """
        try:
            prompt = f"""
基于以下测试用例的执行结果，请优化测试用例以提高成功率：

原始测试用例：
{test_case.json(indent=2)}

执行结果：
{json.dumps(execution_result, indent=2, ensure_ascii=False)}

请分析失败原因并提供优化建议，返回优化后的测试用例JSON格式。
"""
            
            response = self.model.generate_content(prompt)
            
            # 解析优化后的测试用例
            optimized_cases = self._parse_test_cases_response(response.text)
            
            return optimized_cases[0] if optimized_cases else test_case
            
        except Exception as e:
            print(f"优化测试用例失败: {e}")
            return test_case
