"""
任务管理器 - 管理异步任务状态和数据
"""
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
import threading

from schemas.test_schemas import TaskStatus

class TaskManager:
    def __init__(self):
        self.tasks_dir = "assets/tasks"
        os.makedirs(self.tasks_dir, exist_ok=True)
        self._lock = threading.Lock()
    
    def create_task(self, task_id: str, status: str, message: str) -> None:
        """
        创建新任务
        """
        with self._lock:
            task_status = TaskStatus(
                task_id=task_id,
                status=status,
                progress=0.0,
                message=message,
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            
            self._save_task_status(task_status)
    
    def update_task(self, task_id: str, status: str, message: str, progress: float = None) -> None:
        """
        更新任务状态
        """
        with self._lock:
            task_status = self.get_task_status(task_id)
            if task_status:
                task_status.status = status
                task_status.message = message
                task_status.updated_at = datetime.now().isoformat()
                
                if progress is not None:
                    task_status.progress = progress
                elif status == "completed":
                    task_status.progress = 100.0
                elif status == "failed":
                    task_status.progress = 0.0
                
                self._save_task_status(task_status)
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """
        获取任务状态
        """
        try:
            status_file = os.path.join(self.tasks_dir, f"{task_id}_status.json")
            if os.path.exists(status_file):
                with open(status_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return TaskStatus(**data)
            return None
        except Exception as e:
            print(f"获取任务状态失败: {e}")
            return None
    
    def set_task_data(self, task_id: str, data: Dict[str, Any]) -> None:
        """
        设置任务数据
        """
        try:
            data_file = os.path.join(self.tasks_dir, f"{task_id}_data.json")
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存任务数据失败: {e}")
    
    def get_task_data(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务数据
        """
        try:
            data_file = os.path.join(self.tasks_dir, f"{task_id}_data.json")
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            print(f"获取任务数据失败: {e}")
            return None
    
    def delete_task(self, task_id: str) -> bool:
        """
        删除任务
        """
        try:
            with self._lock:
                status_file = os.path.join(self.tasks_dir, f"{task_id}_status.json")
                data_file = os.path.join(self.tasks_dir, f"{task_id}_data.json")
                
                deleted = False
                if os.path.exists(status_file):
                    os.remove(status_file)
                    deleted = True
                
                if os.path.exists(data_file):
                    os.remove(data_file)
                    deleted = True
                
                return deleted
        except Exception as e:
            print(f"删除任务失败: {e}")
            return False
    
    def get_task_history(self, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        获取任务历史记录
        """
        try:
            history = []
            
            # 获取所有状态文件
            status_files = [f for f in os.listdir(self.tasks_dir) if f.endswith('_status.json')]
            
            for status_file in status_files:
                try:
                    with open(os.path.join(self.tasks_dir, status_file), 'r', encoding='utf-8') as f:
                        status_data = json.load(f)
                    
                    task_id = status_data['task_id']
                    
                    # 获取对应的数据文件
                    data_file = os.path.join(self.tasks_dir, f"{task_id}_data.json")
                    task_data = {}
                    if os.path.exists(data_file):
                        with open(data_file, 'r', encoding='utf-8') as f:
                            task_data = json.load(f)
                    
                    # 合并状态和数据
                    history_item = {
                        **status_data,
                        'has_data': bool(task_data),
                        'data_summary': self._get_data_summary(task_data)
                    }
                    
                    history.append(history_item)
                    
                except Exception as e:
                    print(f"处理历史记录文件 {status_file} 失败: {e}")
                    continue
            
            # 按更新时间排序
            history.sort(key=lambda x: x.get('updated_at', ''), reverse=True)
            
            return history
            
        except Exception as e:
            print(f"获取任务历史失败: {e}")
            return []
    
    def _save_task_status(self, task_status: TaskStatus) -> None:
        """
        保存任务状态到文件
        """
        try:
            status_file = os.path.join(self.tasks_dir, f"{task_status.task_id}_status.json")
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(task_status.dict(), f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存任务状态失败: {e}")
    
    def _get_data_summary(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取任务数据摘要
        """
        summary = {}
        
        if 'test_cases' in task_data:
            summary['test_cases_count'] = len(task_data['test_cases'])
        
        if 'step_results' in task_data:
            summary['total_steps'] = len(task_data['step_results'])
            summary['passed_steps'] = sum(1 for r in task_data['step_results'] if r.get('status') == 'success')
        
        if 'url' in task_data:
            summary['target_url'] = task_data['url']
        
        if 'test_name' in task_data:
            summary['test_name'] = task_data['test_name']
        
        return summary
    
    def cleanup_old_tasks(self, older_than_days: int = 30) -> int:
        """
        清理旧任务
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (older_than_days * 24 * 60 * 60)
            
            cleaned_count = 0
            
            for filename in os.listdir(self.tasks_dir):
                file_path = os.path.join(self.tasks_dir, filename)
                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        cleaned_count += 1
            
            return cleaned_count
            
        except Exception as e:
            print(f"清理旧任务失败: {e}")
            return 0
