# AI 服务配置
# 获取 Gemini API Key: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# 浏览器配置
BROWSER_HEADLESS=True
BROWSER_TIMEOUT=30000
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080

# 文件路径配置
ASSETS_DIR=../assets
SCREENSHOTS_DIR=../assets/screenshots
REPORTS_DIR=../assets/reports
TASKS_DIR=../assets/tasks

# 任务配置
MAX_TASK_HISTORY=100
TASK_CLEANUP_DAYS=7

# AI 模型配置
AI_MODEL=gemini-1.5-pro
AI_TEMPERATURE=0.1
AI_MAX_TOKENS=4096

# 测试执行配置
MAX_EXECUTION_TIME=300
MAX_STEPS_PER_TEST=20
SCREENSHOT_QUALITY=80

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log
