"""
报告相关的 API 路由
"""
from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import os
import json
from datetime import datetime, timedelta

from services.task_manager import TaskManager
from services.report_service import ReportService

router = APIRouter()

# 服务实例
task_manager = TaskManager()
report_service = ReportService()

@router.get("/results/{task_id}")
async def get_test_results(task_id: str):
    """
    获取测试执行结果
    """
    try:
        # 从任务管理器获取结果
        task_data = task_manager.get_task_data(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="测试结果不存在")
        
        # 如果是测试执行结果，生成详细报告
        if "step_results" in task_data:
            report = await report_service.generate_detailed_report(task_data)
            return report
        
        return task_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取测试结果失败: {str(e)}")

@router.get("/history")
async def get_test_history(limit: int = 50, offset: int = 0):
    """
    获取测试历史记录
    """
    try:
        history = task_manager.get_task_history(limit=limit, offset=offset)
        return {
            "total": len(history),
            "items": history[offset:offset + limit] if history else []
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取测试历史失败: {str(e)}")

@router.get("/statistics")
async def get_test_statistics(days: int = 7):
    """
    获取测试统计信息
    """
    try:
        # 获取指定天数内的统计
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        stats = await report_service.get_test_statistics(start_date, end_date)
        return stats
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.delete("/results/{task_id}")
async def delete_test_result(task_id: str):
    """
    删除测试结果
    """
    try:
        success = task_manager.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="测试结果不存在")
        
        # 同时删除相关的截图文件
        await report_service.cleanup_task_files(task_id)
        
        return {"message": "测试结果已删除"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除测试结果失败: {str(e)}")

@router.post("/export/{task_id}")
async def export_test_report(task_id: str, format: str = "json"):
    """
    导出测试报告
    """
    try:
        if format not in ["json", "html", "pdf"]:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
        
        task_data = task_manager.get_task_data(task_id)
        if not task_data:
            raise HTTPException(status_code=404, detail="测试结果不存在")
        
        export_result = await report_service.export_report(task_data, format)
        
        return {
            "download_url": export_result["download_url"],
            "file_name": export_result["file_name"],
            "file_size": export_result["file_size"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出报告失败: {str(e)}")
