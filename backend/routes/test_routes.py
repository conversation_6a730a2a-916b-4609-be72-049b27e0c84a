"""
测试相关的 API 路由
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any
import uuid
import asyncio
from datetime import datetime

from schemas.test_schemas import (
    UrlSubmissionRequest, TestCaseGenerationRequest, TestExecutionRequest,
    TestExecutionResult, TaskStatus, TestCase
)
from services.browser_service import BrowserService
from services.ai_service import AIService
from services.test_executor import TestExecutor
from services.task_manager import TaskManager

router = APIRouter()

# 全局服务实例
browser_service = BrowserService()
ai_service = AIService()
test_executor = TestExecutor()
task_manager = TaskManager()

@router.post("/submit-url")
async def submit_url(request: UrlSubmissionRequest, background_tasks: BackgroundTasks):
    """
    提交 URL，启动浏览器截图和 DOM 获取
    """
    try:
        task_id = str(uuid.uuid4())
        
        # 创建任务状态
        task_manager.create_task(
            task_id=task_id,
            status="processing",
            message="正在打开页面并获取信息..."
        )
        
        # 后台任务：获取页面信息
        background_tasks.add_task(
            process_url_submission,
            task_id,
            str(request.url),
            request.description
        )
        
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "页面信息获取中，请稍候..."
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理 URL 失败: {str(e)}")

async def process_url_submission(task_id: str, url: str, description: str = None):
    """
    后台处理 URL 提交
    """
    try:
        # 更新任务状态
        task_manager.update_task(task_id, "processing", "正在截图和获取 DOM...")
        
        # 使用浏览器服务获取页面信息
        page_info = await browser_service.capture_page_info(url)
        
        # 保存页面信息到任务管理器
        task_manager.set_task_data(task_id, {
            "url": url,
            "description": description,
            "screenshot_path": page_info["screenshot_path"],
            "dom_data": page_info["dom_data"],
            "page_title": page_info["title"]
        })
        
        task_manager.update_task(task_id, "completed", "页面信息获取完成")
        
    except Exception as e:
        task_manager.update_task(task_id, "failed", f"获取页面信息失败: {str(e)}")

@router.post("/generate-testcases")
async def generate_testcases(request: TestCaseGenerationRequest, background_tasks: BackgroundTasks):
    """
    基于页面信息生成测试用例
    """
    try:
        task_id = str(uuid.uuid4())
        
        task_manager.create_task(
            task_id=task_id,
            status="processing",
            message="AI 正在分析页面并生成测试用例..."
        )
        
        # 后台任务：生成测试用例
        background_tasks.add_task(
            process_testcase_generation,
            task_id,
            request
        )
        
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "测试用例生成中，请稍候..."
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成测试用例失败: {str(e)}")

async def process_testcase_generation(task_id: str, request: TestCaseGenerationRequest):
    """
    后台处理测试用例生成
    """
    try:
        task_manager.update_task(task_id, "processing", "AI 正在分析页面结构...")
        
        # 使用 AI 服务生成测试用例
        test_cases = await ai_service.generate_test_cases(
            url=request.url,
            screenshot_path=request.screenshot_path,
            dom_data=request.dom_data,
            user_requirements=request.user_requirements
        )
        
        task_manager.set_task_data(task_id, {
            "test_cases": [case.dict() for case in test_cases],
            "generated_at": datetime.now().isoformat()
        })
        
        task_manager.update_task(task_id, "completed", f"成功生成 {len(test_cases)} 个测试用例")
        
    except Exception as e:
        task_manager.update_task(task_id, "failed", f"生成测试用例失败: {str(e)}")

@router.post("/run-testcase")
async def run_testcase(request: TestExecutionRequest, background_tasks: BackgroundTasks):
    """
    执行测试用例
    """
    try:
        task_id = str(uuid.uuid4())
        
        task_manager.create_task(
            task_id=task_id,
            status="running",
            message="测试用例执行中..."
        )
        
        # 后台任务：执行测试
        background_tasks.add_task(
            process_test_execution,
            task_id,
            request
        )
        
        return {
            "task_id": task_id,
            "status": "running",
            "message": "测试执行中，请稍候..."
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行测试失败: {str(e)}")

async def process_test_execution(task_id: str, request: TestExecutionRequest):
    """
    后台处理测试执行
    """
    try:
        task_manager.update_task(task_id, "running", "正在执行测试步骤...")
        
        # 执行测试用例
        result = await test_executor.execute_test_case(
            test_case=request.test_case,
            url=request.url,
            headless=request.headless,
            task_id=task_id
        )
        
        task_manager.set_task_data(task_id, result.dict())
        
        status = "completed" if result.status == "completed" else "failed"
        message = f"测试完成: {result.passed_steps}/{result.total_steps} 步骤通过"
        
        task_manager.update_task(task_id, status, message)
        
    except Exception as e:
        task_manager.update_task(task_id, "failed", f"测试执行失败: {str(e)}")

@router.get("/task-status/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态
    """
    task_status = task_manager.get_task_status(task_id)
    if not task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return task_status

@router.get("/task-data/{task_id}")
async def get_task_data(task_id: str):
    """
    获取任务数据
    """
    task_data = task_manager.get_task_data(task_id)
    if task_data is None:
        raise HTTPException(status_code=404, detail="任务数据不存在")
    
    return task_data
