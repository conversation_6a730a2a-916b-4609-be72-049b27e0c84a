"""
AI 驱动 UI 自动化测试系统 - FastAPI 后端主应用
"""
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from pathlib import Path

from routes.test_routes import router as test_router
from routes.report_routes import router as report_router

# 创建 FastAPI 应用
app = FastAPI(
    title="AI UI Test System",
    description="AI 驱动的 UI 自动化测试系统",
    version="1.0.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],  # Vue 开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建必要的目录
os.makedirs("assets/screenshots", exist_ok=True)
os.makedirs("assets/reports", exist_ok=True)

# 静态文件服务
app.mount("/assets", StaticFiles(directory="assets"), name="assets")

# 注册路由
app.include_router(test_router, prefix="/api/test", tags=["测试"])
app.include_router(report_router, prefix="/api/report", tags=["报告"])

@app.get("/")
async def root():
    return {"message": "AI UI Test System API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
