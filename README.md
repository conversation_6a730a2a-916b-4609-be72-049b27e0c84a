# AI 驱动 UI 自动化测试系统

基于 AI 大模型驱动的智能 UI 测试用例生成与执行平台，支持自动化页面分析、测试用例生成和可视化报告。

## 🚀 功能特性

- **AI 智能分析**: 基于 Gemini 1.5 Pro 大模型，智能分析页面结构
- **自动用例生成**: 自动生成高质量的 UI 测试用例
- **浏览器自动化**: 使用 MidSceneJS + Playwright 控制浏览器执行测试
- **可视化报告**: 生成详细的测试报告，包含步骤截图和执行日志
- **实时监控**: 实时查看测试执行状态和进度
- **历史管理**: 测试历史记录和统计分析

## 🏗️ 技术架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Vue 3     │───▶│  FastAPI    │───▶│   Gemini    │
│   前端界面   │    │   后端API   │    │  AI 模型    │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
                   ┌─────────────┐
                   │ MidSceneJS  │
                   │ 浏览器控制   │
                   └─────────────┘
```

## 📦 项目结构

```
ai-ui-test-system/
├── backend/                 # FastAPI 后端
│   ├── main.py             # 主应用入口
│   ├── routes/             # API 路由
│   ├── services/           # 业务逻辑服务
│   ├── schemas/            # 数据模型
│   └── requirements.txt    # Python 依赖
├── frontend/               # Vue 3 前端
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 通用组件
│   │   ├── stores/         # 状态管理
│   │   └── api/            # API 调用
│   └── package.json        # Node.js 依赖
├── scripts/                # MidSceneJS 执行脚本
│   ├── test_executor.js    # 测试执行器
│   └── package.json        # 脚本依赖
└── assets/                 # 静态资源
    ├── screenshots/        # 截图存储
    ├── reports/            # 报告存储
    └── tasks/              # 任务数据
```

## 🛠️ 安装部署

### 环境要求

- Python 3.10+
- Node.js 16+
- Chrome/Chromium 浏览器

### 1. 克隆项目

```bash
git clone <repository-url>
cd ai-ui-test-system
```

### 2. 后端部署

```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 GEMINI_API_KEY

# 启动后端服务
python main.py
```

### 3. 前端部署

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 4. 脚本环境

```bash
cd scripts

# 安装依赖
npm install

# 安装浏览器
npm run install-browsers
```

## 🔧 配置说明

### 环境变量配置

在 `backend/.env` 文件中配置以下变量：

```env
# AI 服务配置
GEMINI_API_KEY=your_gemini_api_key_here

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=True

# 浏览器配置
BROWSER_HEADLESS=True
BROWSER_TIMEOUT=30000
```

### Gemini API Key 获取

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的 API Key
3. 将 API Key 配置到环境变量中

## 🚀 使用指南

### 1. 启动系统

```bash
# 启动后端 (端口 8000)
cd backend && python main.py

# 启动前端 (端口 3000)
cd frontend && npm run dev
```

### 2. 访问系统

打开浏览器访问: http://localhost:3000

### 3. 使用流程

1. **输入目标网站**: 在首页输入要测试的网站 URL
2. **AI 分析页面**: 系统自动截图并分析页面结构
3. **生成测试用例**: AI 基于页面内容生成测试用例
4. **编辑用例**: 可以手动编辑和调整测试用例
5. **执行测试**: 运行测试用例并查看实时进度
6. **查看报告**: 查看详细的测试报告和截图

## 📊 API 文档

启动后端服务后，访问以下地址查看 API 文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 测试用例格式

系统使用 JSON 格式定义测试用例：

```json
{
  "test_name": "登录测试",
  "description": "测试用户登录功能",
  "steps": [
    {
      "action": "click",
      "selector": "#login-btn",
      "description": "点击登录按钮"
    },
    {
      "action": "type",
      "selector": "#username",
      "value": "testuser",
      "description": "输入用户名"
    },
    {
      "action": "assert",
      "selector": ".welcome",
      "text": "欢迎",
      "description": "验证登录成功"
    }
  ]
}
```

### 支持的操作类型

- `click`: 点击元素
- `type`: 输入文本
- `assert`: 断言验证
- `wait`: 等待
- `navigate`: 页面导航
- `scroll`: 页面滚动

## 🔍 故障排除

### 常见问题

1. **Gemini API 调用失败**
   - 检查 API Key 是否正确配置
   - 确认网络连接正常
   - 检查 API 配额是否充足

2. **浏览器启动失败**
   - 确保已安装 Chrome/Chromium
   - 运行 `npm run install-browsers` 安装 Playwright 浏览器

3. **测试执行失败**
   - 检查目标网站是否可访问
   - 确认选择器是否正确
   - 调整超时时间设置

### 日志查看

- 后端日志: 控制台输出
- 前端日志: 浏览器开发者工具
- 测试日志: 测试报告中的执行日志

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 前端框架
- [FastAPI](https://fastapi.tiangolo.com/) - 后端框架
- [Playwright](https://playwright.dev/) - 浏览器自动化
- [Google Gemini](https://ai.google.dev/) - AI 模型
- [Element Plus](https://element-plus.org/) - UI 组件库

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**注意**: 本系统仅用于学习和测试目的，请遵守目标网站的使用条款和 robots.txt 规则。
